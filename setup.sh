#!/bin/bash

echo "=== Clubbi TypeScript Setup Script ==="
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"
echo ""

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created"
else
    echo "✅ .env file already exists"
fi
echo ""

# Build and start the application
echo "🚀 Building and starting the application..."
docker-compose up --build -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Services are running"
else
    echo "❌ Services failed to start. Check logs with: docker-compose logs"
    exit 1
fi

echo ""
echo "📊 Creating admin user..."
docker-compose exec typescript-app npm run seed:admin

echo ""
echo "🏢 Seeding sample clubs..."
docker-compose exec typescript-app npm run seed:clubs

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📍 Access points:"
echo "   • Admin Dashboard: http://localhost:8001"
echo "   • API Base URL: http://localhost:8001/api"
echo "   • Database: localhost:5433"
echo ""
echo "🔑 Admin credentials:"
echo "   • Username: admin"
echo "   • Password: admin123"
echo ""
echo "🛠️ Useful commands:"
echo "   • View logs: docker-compose logs -f"
echo "   • Stop services: docker-compose down"
echo "   • Restart services: docker-compose restart"
echo "   • Test API: ./test-api.sh"
echo ""
echo "✨ Happy coding!"
