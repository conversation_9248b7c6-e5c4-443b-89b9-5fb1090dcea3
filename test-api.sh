#!/bin/bash

# API Testing Script for Clubbi TypeScript
BASE_URL="http://localhost:8001/api"

echo "=== Clubbi TypeScript API Test ==="
echo "Base URL: $BASE_URL"
echo ""

# Test 1: User Signup
echo "1. Testing User Signup..."
SIGNUP_RESPONSE=$(curl -s -X POST "$BASE_URL/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "mobile_number": "1234567890",
    "password": "password123"
  }')

echo "Signup Response: $SIGNUP_RESPONSE"
TOKEN=$(echo $SIGNUP_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Extracted Token: $TOKEN"
echo ""

# Test 2: User Login
echo "2. Testing User Login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }')

echo "Login Response: $LOGIN_RESPONSE"
echo ""

# Test 3: Get User Info (with token)
if [ ! -z "$TOKEN" ]; then
  echo "3. Testing Get User Info (authenticated)..."
  USER_INFO_RESPONSE=$(curl -s -X GET "$BASE_URL/user-info" \
    -H "Authorization: Bearer $TOKEN")
  
  echo "User Info Response: $USER_INFO_RESPONSE"
  echo ""
fi

# Test 4: Search Clubs
echo "4. Testing Search Clubs..."
SEARCH_RESPONSE=$(curl -s -X GET "$BASE_URL/search-clubs?query=club")
echo "Search Response: $SEARCH_RESPONSE"
echo ""

# Test 5: Filter Clubs
echo "5. Testing Filter Clubs..."
FILTER_RESPONSE=$(curl -s -X GET "$BASE_URL/filter-clubs?rating=4&top_pick=true")
echo "Filter Response: $FILTER_RESPONSE"
echo ""

# Test 6: Get All Clubs
echo "6. Testing Get All Clubs..."
CLUBS_RESPONSE=$(curl -s -X GET "$BASE_URL/clubs")
echo "All Clubs Response: $CLUBS_RESPONSE"
echo ""

# Test 7: Get Club Detail (assuming club ID 1 exists)
echo "7. Testing Get Club Detail (ID: 1)..."
CLUB_DETAIL_RESPONSE=$(curl -s -X GET "$BASE_URL/club/1")
echo "Club Detail Response: $CLUB_DETAIL_RESPONSE"
echo ""

echo "=== API Testing Complete ==="
