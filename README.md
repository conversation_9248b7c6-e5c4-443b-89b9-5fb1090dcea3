# Clubbi TypeScript

A TypeScript/Node.js recreation of the Django Clubbi application with Express.js, TypeORM, and PostgreSQL.

## Features

- **Backend Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with TypeORM ORM
- **Authentication**: JWT-based authentication for API + Session-based for admin dashboard
- **Admin Dashboard**: Web interface for club management
- **REST API**: Complete API matching Django REST framework endpoints
- **Docker Support**: Full containerization with Docker Compose

## Project Structure

```
clubbi-typescript/
├── src/
│   ├── config/          # Database and app configuration
│   ├── controllers/     # Request handlers
│   ├── middleware/      # Authentication and session middleware
│   ├── models/          # TypeORM entities
│   ├── routes/          # Express routes
│   ├── services/        # Business logic
│   ├── types/           # TypeScript interfaces
│   ├── utils/           # Utility functions
│   ├── views/           # EJS templates
│   └── index.ts         # Main application file
├── public/              # Static files
├── docker-compose.yml   # Docker configuration
├── Dockerfile          # Container definition
└── package.json        # Dependencies and scripts
```

## API Endpoints

### Health Check

- `GET /api/health` - Service health check

### Authentication

- `POST /api/signup` - User registration
- `POST /api/login` - User login
- `GET /api/user-info` - Get authenticated user info (requires JWT)

### Clubs

- `GET /api/search-clubs?query=<name>` - Search clubs by name
- `GET /api/filter-clubs?rating=<min>&top_pick=<bool>&offers=<bool>&nearby=<bool>&lower_price=<bool>` - Filter clubs
- `GET /api/club/<id>` - Get club details
- `POST /api/clubs` - Create club (requires JWT + admin)
- `PUT /api/clubs/<id>` - Update club (requires JWT + admin)
- `DELETE /api/clubs/<id>` - Delete club (requires JWT + admin)

### Admin Dashboard

- `GET /` - Admin login page
- `GET /dashboard` - Admin dashboard (requires session)
- `GET /add-club` - Add club form
- `GET /update-club/<id>` - Edit club form
- `GET /delete-club/<id>` - Delete club
- `GET /logout` - Logout

## Setup Instructions

### Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose (for containerized setup)

### Option 1: Quick Setup (Recommended)

1. **Navigate to the project:**

   ```bash
   cd clubbi-typescript
   ```

2. **Run the setup script:**

   ```bash
   ./setup.sh
   ```

   This script will:

   - Create the `.env` file
   - Build and start Docker containers
   - Create an admin user
   - Seed sample clubs
   - Display access information

3. **Access the application:**
   - Admin Dashboard: http://localhost:8001
   - API Base URL: http://localhost:8001/api

### Option 2: Manual Docker Setup

1. **Create environment file:**

   ```bash
   cp .env.example .env
   ```

2. **Start the application:**

   ```bash
   docker-compose up --build -d
   ```

3. **Create admin user and seed data:**

   ```bash
   docker-compose exec typescript-app npm run seed:all
   ```

4. **Access the application:**
   - Admin Dashboard: http://localhost:8001
   - API Base URL: http://localhost:8001/api

### Option 3: Local Development Setup

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Set up environment variables:**

   ```bash
   cp .env.example .env
   # Edit .env with your database configuration
   ```

3. **Set up PostgreSQL database:**

   - Create a PostgreSQL database named `clubbi`
   - Update database credentials in `.env`

4. **Build and start the application:**

   ```bash
   npm run build
   npm start

   # Or for development with auto-reload:
   npm run dev
   ```

## Environment Variables

```env
# Database Configuration
DATABASE_TYPE=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=clubbidevuser
DATABASE_PASSWORD=clubbi%^*user1234$
DATABASE_NAME=clubbi

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=8000
NODE_ENV=development

# Session Configuration
SESSION_SECRET=your-session-secret-here

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
```

## Database Models

### User

- id, username, email, mobile_number
- password (hashed), is_admin, is_superuser
- is_deleted, is_active, first_name, last_name
- date_joined, last_login

### Club

- id, name, description, address
- google_maps_link, rating, image_url
- additional_images (JSON), opening_hours (JSON)
- top_pick, offers, nearby, lower_price (booleans)
- created_at, updated_at

## Authentication

### JWT Authentication (API)

- Include JWT token in Authorization header: `Bearer <token>`
- Tokens expire in 7 days (configurable)
- Required for protected API endpoints

### Session Authentication (Admin Dashboard)

- Cookie-based sessions for web interface
- Required for admin dashboard access
- Admin users need `is_admin` or `is_superuser` flag

## Development

### Available Scripts

- `npm run dev` - Start development server with auto-reload
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server
- `npm run typeorm` - Run TypeORM CLI commands

### Database Migrations

```bash
# Generate migration
npm run migration:generate -- src/migrations/MigrationName

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## Migration from Django

This TypeScript application is designed as a drop-in replacement for the Django version:

1. **Same API endpoints and request/response formats**
2. **Compatible database schema** (with minor TypeORM adaptations)
3. **Identical admin dashboard functionality**
4. **Same authentication flows** (JWT for API, sessions for admin)
5. **Preserved business logic** and validation rules

### Key Differences

- JWT tokens instead of Django's token authentication
- TypeORM entities instead of Django models
- Express.js routes instead of Django URLs
- EJS templates instead of Django templates
- Environment variables instead of Django settings

## Testing the Application

### Create Admin User

You'll need to create an admin user directly in the database or add a seeding script.

### Test API Endpoints

```bash
# Register user
curl -X POST http://localhost:8001/api/signup \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST http://localhost:8001/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'

# Search clubs
curl http://localhost:8001/api/search-clubs?query=club
```

## Production Deployment

1. Set `NODE_ENV=production` in environment
2. Use proper JWT and session secrets
3. Configure CORS for your domain
4. Set up SSL/TLS termination
5. Use a process manager like PM2
6. Set up database backups
7. Configure logging and monitoring
