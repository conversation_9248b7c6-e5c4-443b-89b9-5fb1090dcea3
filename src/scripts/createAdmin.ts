import { AppDataSource } from '../config/database';
import { User } from '../models/User';
import dotenv from 'dotenv';

dotenv.config();

const createAdminUser = async () => {
  try {
    await AppDataSource.initialize();
    console.log('Database connected');

    const userRepository = AppDataSource.getRepository(User);

    // Check if admin user already exists
    const existingAdmin = await userRepository.findOne({
      where: { username: 'admin' }
    });

    if (existingAdmin) {
      console.log('Admin user already exists');
      process.exit(0);
    }

    // Create admin user
    const adminUser = new User();
    adminUser.username = 'admin';
    adminUser.email = '<EMAIL>';
    adminUser.password = 'admin123'; // Will be hashed by the @BeforeInsert hook
    adminUser.is_admin = true;
    adminUser.is_superuser = true;
    adminUser.first_name = 'Admin';
    adminUser.last_name = 'User';

    await userRepository.save(adminUser);
    console.log('Admin user created successfully');
    console.log('Username: admin');
    console.log('Password: admin123');
    console.log('Email: <EMAIL>');

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

createAdminUser();
