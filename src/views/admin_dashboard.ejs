<!DOCTYPE html>
<html>
<head>
    <title>Admin Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f8;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        h2, h3 {
            color: #0056b3;
        }

        .dashboard-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        .dashboard-container h2 {
            text-align: center;
            font-size: 1.8em;
        }

        .manage-clubs {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            background-color: #fff;
        }

        table th, table td {
            padding: 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }

        table th {
            background-color: #007bff;
            color: #fff;
        }

        table tr:hover {
            background-color: #f1f1f1;
        }

        .actions a {
            padding: 6px 12px;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 5px;
        }

        .actions .edit {
            background-color: #28a745;
        }

        .actions .delete {
            background-color: #dc3545;
        }

        .add-club {
            display: inline-block;
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: #fff;
            border-radius: 4px;
            text-decoration: none;
            text-align: center;
        }
        .logout-button {
            display: inline-block;
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #ff6b6b;
            color: #fff;
            border-radius: 4px;
            text-decoration: none;
            text-align: center;
        }

        .logout-container {
            text-align: right;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="logout-container">
            <a href="/logout" class="logout-button">Logout</a>
        </div>
        <h2>Admin Dashboard</h2>
        <div class="manage-clubs">
            <h3>Manage Clubs</h3>
            <a href="/add-club" class="add-club">Add New Club</a>
        </div>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Rating</th>
                        <th>Address</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% clubs.forEach(function(club) { %>
                        <tr>
                            <td><%= club.name %></td>
                            <td><%= club.rating %></td>
                            <td><%= club.address %></td>
                            <td class="actions">
                                <a href="/update-club/<%= club.id %>" class="edit">Edit</a>
                                <a href="/delete-club/<%= club.id %>" class="delete" onclick="return confirm('Are you sure you want to delete this club?')">Delete</a>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
