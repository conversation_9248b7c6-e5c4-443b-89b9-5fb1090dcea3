<!DOCTYPE html>
<html>
<head>
    <title><%= club ? 'Edit' : 'Add' %> Club</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f8;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        .form-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        h2 {
            color: #0056b3;
            text-align: center;
            font-size: 1.8em;
        }

        form {
            display: flex;
            flex-direction: column;
        }

        label {
            font-weight: bold;
            margin-top: 10px;
            color: #333;
        }

        input[type="text"],
        input[type="url"],
        input[type="number"],
        input[type="checkbox"],
        textarea {
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
        }

        textarea {
            height: 80px;
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-top: 5px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        button {
            margin-top: 20px;
            padding: 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #007bff;
            text-decoration: none;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2><%= club ? 'Edit' : 'Add' %> Club</h2>
        
        <form method="POST">
            <label for="name">Club Name:</label>
            <input type="text" name="name" id="name" value="<%= club ? club.name : '' %>" required>
            
            <label for="description">Description:</label>
            <textarea name="description" id="description" required><%= club ? club.description : '' %></textarea>
            
            <label for="address">Address:</label>
            <input type="text" name="address" id="address" value="<%= club ? club.address : '' %>" required>
            
            <label for="google_maps_link">Google Maps Link:</label>
            <input type="url" name="google_maps_link" id="google_maps_link" value="<%= club ? club.google_maps_link : '' %>">
            
            <label for="rating">Rating:</label>
            <input type="number" name="rating" id="rating" step="0.1" max="5" min="0" value="<%= club ? club.rating : '' %>" required>
            
            <label for="image_url">Main Image URL:</label>
            <input type="url" name="image_url" id="image_url" value="<%= club ? club.image_url : '' %>" required>
            
            <label for="opening_hours">Opening Hours (JSON format):</label>
            <textarea name="opening_hours" id="opening_hours"><%= club ? JSON.stringify(club.opening_hours) : '{}' %></textarea>
            
            <div class="checkbox-group">
                <input type="checkbox" name="top_pick" id="top_pick" <%= club && club.top_pick ? 'checked' : '' %>>
                <label for="top_pick">Top Pick</label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" name="offers" id="offers" <%= club && club.offers ? 'checked' : '' %>>
                <label for="offers">Has Offers</label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" name="nearby" id="nearby" <%= club && club.nearby ? 'checked' : '' %>>
                <label for="nearby">Nearby</label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" name="lower_price" id="lower_price" <%= club && club.lower_price ? 'checked' : '' %>>
                <label for="lower_price">Lower Price</label>
            </div>
            
            <button type="submit"><%= club ? 'Update' : 'Add' %> Club</button>
        </form>
        
        <a href="/dashboard" class="back-link">Back to Dashboard</a>
    </div>
</body>
</html>
