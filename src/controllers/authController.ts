import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { userSignupSchema, userLoginSchema } from '../utils/validation';
import { AuthenticatedRequest } from '../middleware/auth';

export class AuthController {
  private authService = new AuthService();

  signup = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = userSignupSchema.validate(req.body);
      
      if (error) {
        res.status(400).json({ message: error.details[0].message });
        return;
      }

      const result = await this.authService.signup(value);
      res.status(201).json(result);
    } catch (error) {
      if (error instanceof Error) {
        res.status(400).json({ message: error.message });
      } else {
        res.status(400).json({ message: 'Try Again.' });
      }
    }
  };

  login = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = userLoginSchema.validate(req.body);
      
      if (error) {
        res.status(400).json({ error: error.details[0].message });
        return;
      }

      const result = await this.authService.login(value);
      res.status(200).json(result);
    } catch (error) {
      if (error instanceof Error) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(400).json({ error: 'Invalid credentials' });
      }
    }
  };

  getUserInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const user = await this.authService.getUserById(req.user.userId);
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      res.status(200).json({
        username: user.username,
        email: user.email
      });
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };
}
