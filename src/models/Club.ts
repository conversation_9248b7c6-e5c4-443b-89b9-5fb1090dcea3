import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('clubs')
export class Club {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column('text')
  description: string;

  @Column({ length: 200 })
  address: string;

  @Column({ default: '' })
  google_maps_link: string;

  @Column('decimal', { precision: 3, scale: 1 })
  rating: number;

  @Column({ default: '' })
  image_url: string;

  @Column('json', { default: [] })
  additional_images: string[];

  @Column('json', { default: {} })
  opening_hours: Record<string, any>;

  @Column({ default: false })
  top_pick: boolean;

  @Column({ default: false })
  offers: boolean;

  @Column({ default: false })
  nearby: boolean;

  @Column({ default: false })
  lower_price: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
